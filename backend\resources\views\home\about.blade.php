@extends('layouts.marketing')

@section('title', 'About Us')

@section('content')
<!-- Hero Section -->
<div class="relative overflow-hidden bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 py-20">
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-5xl md:text-6xl font-extrabold mb-6">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                About GOC Agent
            </span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto">
            We're building the future of AI-powered development tools to make coding more efficient and enjoyable.
        </p>
    </div>
</div>

<!-- Mission Section -->
<div class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold mb-8">
            <span class="bg-gradient-to-r from-orange-600 to-purple-600 bg-clip-text text-transparent">
                Our Mission
            </span>
        </h2>
        <p class="text-xl text-gray-700 leading-relaxed mb-12">
            To democratize AI-powered development tools and make advanced coding assistance accessible to developers worldwide. 
            We believe that every developer should have access to intelligent tools that enhance their productivity and creativity.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-orange-400 to-pink-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
                <p class="text-gray-600">Pushing the boundaries of what's possible with AI-assisted development.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Community</h3>
                <p class="text-gray-600">Building a supportive community of developers who learn and grow together.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Trust</h3>
                <p class="text-gray-600">Maintaining the highest standards of security and privacy for your code.</p>
            </div>
        </div>
    </div>
</div>

<!-- Story Section -->
<div class="py-20 bg-gradient-to-b from-gray-50 to-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-4xl font-bold text-center mb-12">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Our Story
            </span>
        </h2>
        
        <div class="prose prose-lg mx-auto text-gray-700">
            <p class="text-xl leading-relaxed mb-8">
                GOC Agent was born from a simple observation: developers spend too much time on repetitive tasks 
                and not enough time on creative problem-solving. We set out to change that by creating an AI 
                assistant that truly understands code and can adapt to each developer's unique style.
            </p>
            
            <p class="text-lg leading-relaxed mb-8">
                What started as a weekend project has evolved into a comprehensive platform that supports multiple 
                AI providers, learns from your coding patterns, and integrates seamlessly with your existing workflow. 
                We've built GOC Agent to be the coding companion we always wished we had.
            </p>
            
            <p class="text-lg leading-relaxed">
                Today, thousands of developers use GOC Agent to write better code faster, learn new technologies, 
                and focus on what they love most about programming: building amazing things.
            </p>
        </div>
    </div>
</div>

<!-- Values Section -->
<div class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-4xl font-bold text-center mb-16">
            <span class="bg-gradient-to-r from-orange-600 to-purple-600 bg-clip-text text-transparent">
                Our Values
            </span>
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-orange-400 to-pink-400 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Developer-First</h3>
                    <p class="text-gray-600">Every decision we make is guided by what's best for developers and their workflow.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Continuous Innovation</h3>
                    <p class="text-gray-600">We're constantly improving and adding new features based on user feedback.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Open & Transparent</h3>
                    <p class="text-gray-600">We believe in transparency in our processes, pricing, and communication.</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Quality First</h3>
                    <p class="text-gray-600">We prioritize quality and reliability over quick fixes and shortcuts.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="py-20 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold text-white mb-6">
            Join Our Journey
        </h2>
        <p class="text-xl text-white/90 mb-8">
            Be part of the future of AI-powered development. Start using GOC Agent today.
        </p>
        <a href="{{ route('register') }}" class="inline-flex items-center px-8 py-4 text-lg font-semibold rounded-xl text-purple-600 bg-white hover:bg-gray-50 transform hover:scale-105 transition-all duration-200 shadow-xl">
            Get Started Free
        </a>
    </div>
</div>
@endsection
