@extends('layouts.marketing')

@section('title', 'Documentation')

@section('content')
<!-- Hero Section -->
<div class="relative overflow-hidden bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-20">
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-5xl md:text-6xl font-extrabold mb-6">
            <span class="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Documentation
            </span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto">
            Everything you need to get started with GOC Agent and maximize your productivity.
        </p>
    </div>
</div>

<!-- Quick Start Section -->
<div class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold mb-4">
                <span class="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                    Quick Start Guide
                </span>
            </h2>
            <p class="text-xl text-gray-600">Get up and running in minutes</p>
        </div>
        
        <div class="space-y-8">
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        1
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Create Your Account</h3>
                        <p class="text-gray-700 mb-4">Sign up for a free account to get started with GOC Agent.</p>
                        <a href="{{ route('register') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-colors">
                            Sign Up Now
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        2
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Install the CLI</h3>
                        <p class="text-gray-700 mb-4">Install the GOC Agent CLI tool globally on your system.</p>
                        <div class="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
                            npm install -g goc-agent
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-2xl p-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        3
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Configure Your API Keys</h3>
                        <p class="text-gray-700 mb-4">Set up your API keys for your preferred AI providers.</p>
                        <div class="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
                            goc config
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-cyan-50 to-blue-50 rounded-2xl p-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        4
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Start Coding</h3>
                        <p class="text-gray-700 mb-4">Begin using GOC Agent in your projects.</p>
                        <div class="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
                            goc chat "Help me build a todo app"
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documentation Sections -->
<div class="py-20 bg-gradient-to-b from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold mb-4">
                <span class="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    Documentation Sections
                </span>
            </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Getting Started</h3>
                <p class="text-gray-600 mb-4">Installation, setup, and basic usage guide.</p>
                <a href="#" class="text-green-600 hover:text-green-700 font-medium">Read more →</a>
            </div>
            
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">API Reference</h3>
                <p class="text-gray-600 mb-4">Complete API documentation and examples.</p>
                <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium">Read more →</a>
            </div>
            
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-teal-400 to-cyan-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Advanced Usage</h3>
                <p class="text-gray-600 mb-4">Advanced features and customization options.</p>
                <a href="#" class="text-teal-600 hover:text-teal-700 font-medium">Read more →</a>
            </div>
            
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Team Setup</h3>
                <p class="text-gray-600 mb-4">Configure GOC Agent for team collaboration.</p>
                <a href="#" class="text-cyan-600 hover:text-cyan-700 font-medium">Read more →</a>
            </div>
            
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Troubleshooting</h3>
                <p class="text-gray-600 mb-4">Common issues and solutions.</p>
                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Read more →</a>
            </div>
            
            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-xl flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Examples</h3>
                <p class="text-gray-600 mb-4">Real-world examples and use cases.</p>
                <a href="#" class="text-indigo-600 hover:text-indigo-700 font-medium">Read more →</a>
            </div>
        </div>
    </div>
</div>
@endsection
